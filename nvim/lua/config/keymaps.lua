vim.g.mapleader = "\\"

vim.keymap.set("n", "<leader><space>", ":nohl<CR>", { noremap = true, silent = true })
vim.keymap.set("n", "<leader>ff", "<cmd>Telescope find_files<CR>", { noremap = true, silent = true })
vim.keymap.set("n", "<leader>fg", "<cmd>Telescope live_grep<CR>", { noremap = true, silent = true })
vim.keymap.set("n", "<leader>fd", "<cmd>Telescope diagnostics<CR>", { noremap = true, silent = true })
vim.keymap.set("n", "<leader>fb", "<cmd>Telescope buffers<CR>", { noremap = true, silent = true })
vim.keymap.set("n", "<leader>fh", "<cmd>Telescope help_tags<CR>", { noremap = true, silent = true })
vim.keymap.set("n", "<leader>m", require("treesj").toggle)
vim.keymap.set("n", "<leader>M", function()
	require("treesj").toggle({ split = { recursive = true } })
end)
vim.keymap.set("n", "<leader>xo", "<cmd>cwindow<cr>", { noremap = true, silent = true })
vim.keymap.set("n", "<leader>xd", function()
	vim.diagnostic.setqflist()
end)
vim.keymap.set("n", "<leader>xr", vim.lsp.buf.references)
vim.keymap.set("n", "]q", "<cmd>cnext<cr>", { noremap = true, silent = true })
vim.keymap.set("n", "[q", "<cmd>cprev<cr>", { noremap = true, silent = true })
vim.keymap.set("n", "<leader>qD", function()
	vim.diagnostic.setqflist({ all = true })
end)
